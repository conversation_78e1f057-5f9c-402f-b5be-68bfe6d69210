/* Modern Homepage Styles */

/* Main Container */
.home-container {
  @apply bg-gradient-to-br from-slate-50 via-white to-blue-50 h-full w-full relative mb-20;
}

.home-row {
  @apply flex flex-row w-full;
}

/* Modern Hero Section */
.modern-hero-section {
  @apply relative text-white overflow-hidden;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.modern-hero-content {
  @apply max-w-7xl mx-auto px-4 relative z-20;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  height: 100%;
  max-height: calc(100vh - 4rem);
}

.modern-hero-left {
  @apply flex flex-col justify-center;
  padding-right: 2rem;
}

.modern-hero-right {
  @apply flex justify-center items-center;
  padding-left: 2rem;
}

.modern-hero-text {
  @apply text-left;
  margin-bottom: 2rem;
}

.modern-hero-title {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-4;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.02em;
}

.modern-hero-subtitle {
  @apply text-base md:text-lg text-gray-300 leading-relaxed max-w-2xl;
  font-family: "Inter", sans-serif;
  opacity: 0.9;
}

.modern-hero-visual {
  @apply flex justify-center items-center;
}

.modern-globe-container {
  @apply relative flex items-center justify-center;
}

.modern-globe-interactive {
  @apply w-[500px] h-[500px];
  filter: drop-shadow(0 20px 40px rgba(59, 130, 246, 0.3));
}

.modern-hero-actions {
  @apply flex flex-col sm:flex-row gap-4 justify-start items-start;
}

.modern-cta-button {
  @apply px-8 py-4 rounded-full font-semibold text-base transition-all duration-300 relative overflow-hidden;
  font-family: "Inter", sans-serif;
  min-width: 160px;
}

.modern-cta-primary {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-700 transform hover:scale-105;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.modern-cta-primary:hover {
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
}

.modern-cta-secondary {
  @apply bg-transparent text-white border-2 border-white/30 hover:border-white/60 hover:bg-white/10;
  backdrop-filter: blur(10px);
}

/* Modern CTA Buttons */
.homepage-cta-button {
  @apply inline-flex items-center justify-center px-8 py-4 text-base font-bold rounded-2xl transition-all duration-500 transform relative overflow-hidden;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.02em;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.homepage-cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.homepage-cta-button:hover::before {
  left: 100%;
}

.homepage-cta-primary {
  @apply bg-white text-GTI-BLUE-default;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.homepage-cta-primary:hover {
  @apply transform scale-110 shadow-2xl;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

.homepage-cta-secondary {
  @apply bg-transparent text-white border-2 border-white/50;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  backdrop-filter: blur(10px);
}

.homepage-cta-secondary:hover {
  @apply bg-white text-GTI-BLUE-default transform scale-110;
  box-shadow: 0 20px 40px rgba(255, 255, 255, 0.2);
}

/* Section Styles */
.homepage-section {
  @apply py-16 px-4;
}

.homepage-section-title {
  @apply text-3xl md:text-5xl font-bold text-GTI-BLUE-default mb-12 text-center;
}

.homepage-section-subtitle {
  @apply text-lg md:text-xl text-gray-600 mb-16 text-center max-w-4xl mx-auto leading-relaxed;
}

/* Modern Service Cards */
.homepage-card {
  @apply relative bg-white rounded-3xl overflow-hidden transition-all duration-500 cursor-pointer border border-gray-100;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  );
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

/* Group class is applied in JSX for hover effects */

.homepage-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.05)
  );
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.homepage-card:hover::before {
  opacity: 1;
}

.homepage-card-header {
  @apply relative p-8 text-center z-10;
}

.homepage-card-content {
  @apply relative p-8 pt-4 z-10;
}

.homepage-card-icon {
  @apply w-20 h-20 mx-auto mb-6 transition-all duration-300 flex items-center justify-center;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border-radius: 20px;
}

.homepage-card-title {
  @apply text-xl md:text-2xl font-bold mb-4 transition-colors duration-300;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.homepage-card:hover .homepage-card-title {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.homepage-card-description {
  @apply text-gray-600 leading-relaxed text-base transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.homepage-card:hover .homepage-card-description {
  @apply text-gray-700;
}

/* Modern Globe Section */
.modern-globe-section {
  @apply relative w-full max-w-5xl mx-auto flex items-center justify-center px-8;
  min-height: 500px;
  margin-top: 20px;
}

.modern-globe-container {
  @apply relative flex items-center justify-center;
}

.modern-globe-image {
  @apply w-72 h-72 lg:w-96 lg:h-96 object-contain;
  animation: float 6s ease-in-out infinite;
  filter: drop-shadow(0 10px 30px rgba(59, 130, 246, 0.3));
}

.modern-globe-replacement {
  @apply w-72 h-72 lg:w-96 lg:h-96;
  filter: drop-shadow(0 10px 30px rgba(59, 130, 246, 0.3));
}

.modern-globe-glow {
  @apply absolute inset-0 rounded-full;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.2) 0%,
    transparent 70%
  );
  animation: pulse 4s ease-in-out infinite;
}

.modern-globe-features {
  @apply absolute inset-0 pointer-events-none;
}

.modern-feature-card {
  @apply absolute pointer-events-auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 180px;
  max-width: 180px;
}

.modern-feature-card:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

.modern-feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.05)
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-feature-card:hover::before {
  opacity: 1;
}

.modern-feature-icon {
  @apply flex items-center justify-center w-12 h-12 rounded-xl mb-3;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.modern-feature-card:hover .modern-feature-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(6, 182, 212, 0.2)
  );
}

.modern-feature-content {
  @apply relative z-10;
}

.modern-feature-title {
  @apply text-sm font-bold mb-2 transition-colors duration-300;
  color: #1e293b;
  font-family: "Inter", sans-serif;
}

.modern-feature-card:hover .modern-feature-title {
  @apply text-GTI-BLUE-default;
}

.modern-feature-description {
  @apply text-gray-600 text-xs leading-relaxed transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.modern-feature-card:hover .modern-feature-description {
  @apply text-gray-700;
}

/* Feature Card Positioning */
.feature-top-left {
  top: 0px;
  left: -50px;
  animation: fadeInLeft 1s ease-out 0.2s both;
}

.feature-top-right {
  top: 0px;
  right: -50px;
  animation: fadeInRight 1s ease-out 0.4s both;
}

.feature-bottom-left {
  bottom: 0px;
  left: -50px;
  animation: fadeInLeft 1s ease-out 0.6s both;
}

.feature-bottom-right {
  bottom: 0px;
  right: -50px;
  animation: fadeInRight 1s ease-out 0.8s both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Globe Ecosystem Layout */
.globe-ecosystem {
  @apply relative w-full max-w-6xl mx-auto;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  grid-template-rows: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  justify-items: center;
  height: 550px;
  max-height: 60vh;
}

.globe-center {
  grid-column: 2;
  grid-row: 2;
  @apply relative z-10;
}

.globe-features-grid {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 1rem;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Feature Card Positioning */
.feature-card {
  @apply bg-white/15 backdrop-blur-md rounded-xl p-4 border border-white/30;
  @apply transition-all duration-300 hover:bg-white/25 hover:scale-105;
  @apply shadow-lg hover:shadow-xl;
  pointer-events: auto;
  max-width: 200px;
  animation: float 6s ease-in-out infinite;
}

.feature-card-1 {
  grid-column: 1;
  grid-row: 1;
  justify-self: start;
  align-self: start;
  animation-delay: 0s;
}

.feature-card-2 {
  grid-column: 3;
  grid-row: 1;
  justify-self: end;
  align-self: start;
  animation-delay: 1.5s;
}

.feature-card-3 {
  grid-column: 1;
  grid-row: 3;
  justify-self: start;
  align-self: end;
  animation-delay: 3s;
}

.feature-card-4 {
  grid-column: 3;
  grid-row: 3;
  justify-self: end;
  align-self: end;
  animation-delay: 4.5s;
}

.feature-icon {
  @apply w-10 h-10 bg-GTI-BLUE-default/20 rounded-lg flex items-center justify-center mb-3;
}

.feature-content {
  @apply text-left;
}

.feature-title {
  @apply text-sm font-semibold text-white mb-1;
}

.feature-description {
  @apply text-xs text-gray-200 leading-relaxed;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Mobile Responsive Hero */
@media (max-width: 1024px) {
  .modern-hero-section {
    height: 100vh;
    padding: 1.5rem 0;
  }

  .modern-hero-content {
    gap: 2rem;
    max-height: calc(100vh - 3rem);
  }

  .modern-hero-left {
    padding-right: 1rem;
  }

  .modern-hero-right {
    padding-left: 1rem;
  }

  .modern-hero-title {
    @apply text-2xl md:text-3xl;
  }

  .modern-hero-subtitle {
    @apply text-sm md:text-base;
  }

  .globe-ecosystem {
    height: 400px;
    max-height: 45vh;
    gap: 1.5rem;
  }

  .modern-globe-interactive {
    @apply w-80 h-80;
  }

  .feature-card {
    max-width: 160px;
    @apply p-3 bg-white/15;
  }

  .feature-title {
    @apply text-sm font-semibold;
  }

  .feature-description {
    @apply text-xs text-gray-100;
  }
}

@media (max-width: 768px) {
  .modern-hero-section {
    height: 100vh;
    padding: 1rem 0;
  }

  .modern-hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-height: calc(100vh - 2rem);
    padding: 0 1rem;
    text-align: center;
  }

  .modern-hero-left {
    padding-right: 0;
    order: 1;
  }

  .modern-hero-right {
    padding-left: 0;
    order: 2;
  }

  .modern-hero-text {
    @apply text-center;
    margin-bottom: 1.5rem;
  }

  .modern-hero-actions {
    @apply justify-center items-center;
  }

  .modern-hero-title {
    @apply text-xl md:text-2xl;
    margin-bottom: 0.5rem;
  }

  .modern-hero-subtitle {
    @apply text-sm;
    margin-bottom: 1rem;
  }

  .globe-ecosystem {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: auto;
    max-height: 55vh;
  }

  .modern-globe-interactive {
    @apply w-72 h-72;
  }

  .globe-features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 0.5rem;
    width: 100%;
    height: auto;
  }

  .feature-card-1,
  .feature-card-2,
  .feature-card-3,
  .feature-card-4 {
    grid-column: auto;
    grid-row: auto;
    justify-self: center;
    align-self: center;
    max-width: 160px;
    @apply p-3 bg-white/15 border border-white/30;
  }

  .modern-hero-actions {
    @apply flex-col gap-2 justify-center items-center;
    margin-top: 1rem;
  }

  .modern-cta-button {
    @apply px-4 py-2 text-sm;
    min-width: 120px;
  }
}

/* Modern Showcase Section */
.modern-showcase-section {
  @apply py-16 px-4 relative overflow-hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.modern-showcase-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(59, 130, 246, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(6, 182, 212, 0.05) 0%,
      transparent 50%
    );
  z-index: 1;
}

.showcase-container {
  @apply relative z-10 space-y-12;
}

.showcase-header {
  @apply text-center space-y-6;
}

.showcase-badge {
  @apply inline-flex items-center space-x-2 px-4 py-2 rounded-full;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.badge-icon {
  @apply text-xl;
  animation: pulse 2s ease-in-out infinite;
}

.badge-text {
  @apply text-GTI-BLUE-default font-semibold text-sm;
  font-family: "Inter", sans-serif;
}

.showcase-title {
  @apply text-4xl lg:text-5xl font-bold leading-tight;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
}

.showcase-subtitle {
  @apply text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto;
  font-family: "Inter", sans-serif;
}

/* Benefits Grid */
.showcase-benefits {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

.benefit-card {
  @apply bg-white rounded-2xl p-8 text-center transition-all duration-500 hover:shadow-2xl hover:-translate-y-2;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.benefit-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.15);
}

.benefit-icon {
  @apply text-5xl mb-6 mx-auto w-20 h-20 flex items-center justify-center rounded-2xl;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(6, 182, 212, 0.2)
  );
}

.benefit-title {
  @apply text-xl font-bold mb-4 text-gray-800;
  font-family: "Inter", sans-serif;
}

.benefit-description {
  @apply text-gray-600 leading-relaxed;
  font-family: "Inter", sans-serif;
}

/* Stats Section */
.showcase-stats {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-8;
}

.stat-card {
  @apply bg-white rounded-2xl p-6 text-center transition-all duration-300 hover:shadow-lg;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-number {
  @apply block text-3xl font-bold text-GTI-BLUE-default mb-2;
  font-family: "Inter", sans-serif;
}

.stat-label {
  @apply text-gray-600 text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* CTA Section */
.showcase-cta {
  @apply text-center space-y-6;
}

.showcase-button {
  @apply inline-flex items-center space-x-3 px-12 py-6 text-xl font-bold rounded-2xl transition-all duration-500 transform relative overflow-hidden;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  color: white;
  font-family: "Inter", sans-serif;
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.3);
}

.showcase-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.showcase-button:hover::before {
  left: 100%;
}

.showcase-button:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 25px 60px rgba(59, 130, 246, 0.4);
}

.cta-note {
  @apply text-gray-600 text-lg;
  font-family: "Inter", sans-serif;
}

/* Modern Supply Chain Section */
.modern-supply-chain-section {
  @apply py-20 px-4 relative overflow-hidden;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 100%);
}

.modern-supply-chain-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(59, 130, 246, 0.1) 0%,
    transparent 50%
  );
  z-index: 1;
}

.supply-chain-container {
  @apply relative z-10 space-y-16;
}

.supply-chain-header {
  @apply text-center space-y-6;
}

.supply-chain-badge {
  @apply inline-flex items-center space-x-2 px-4 py-2 rounded-full;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.supply-chain-title {
  @apply text-4xl lg:text-5xl font-bold leading-tight;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
}

.supply-chain-subtitle {
  @apply text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto;
  font-family: "Inter", sans-serif;
}

.supply-chain-content {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-16 items-center;
}

.supply-chain-info {
  @apply space-y-8;
}

.info-card {
  @apply bg-white rounded-3xl p-8 transition-all duration-500 hover:shadow-2xl hover:-translate-y-1;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  );
  backdrop-filter: blur(20px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-content {
  @apply space-y-8;
}

.info-description {
  @apply text-lg text-gray-700 leading-relaxed;
  font-family: "Inter", sans-serif;
}

.info-features {
  @apply space-y-4;
}

.feature-item {
  @apply flex items-center space-x-3;
}

.feature-icon {
  @apply w-6 h-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center text-sm font-bold;
}

.feature-text {
  @apply text-gray-700 font-medium;
  font-family: "Inter", sans-serif;
}

.supply-chain-cta {
  @apply pt-4;
}

.supply-chain-button {
  @apply inline-flex items-center space-x-3 px-10 py-4 text-lg font-bold rounded-2xl transition-all duration-500 transform relative overflow-hidden;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  color: white;
  font-family: "Inter", sans-serif;
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
}

.supply-chain-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.supply-chain-button:hover::before {
  left: 100%;
}

.supply-chain-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 20px 50px rgba(59, 130, 246, 0.4);
}

.supply-chain-visual {
  @apply flex justify-center items-center;
}

.visual-container {
  @apply relative;
}

.supply-chain-image {
  @apply w-full max-w-lg h-auto object-contain transform hover:scale-105 transition-transform duration-500 relative z-10;
}

.visual-glow {
  @apply absolute inset-0 rounded-full opacity-30;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.2) 0%,
    transparent 70%
  );
  filter: blur(40px);
  animation: pulse 3s ease-in-out infinite;
}

/* Modern Users Section */
.modern-users-section {
  @apply py-20 px-4 relative overflow-hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
}

.modern-users-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 30%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 70%,
      rgba(6, 182, 212, 0.03) 0%,
      transparent 50%
    );
  z-index: 1;
}

.users-header {
  @apply relative z-10 text-center space-y-6 mb-16;
}

.users-badge {
  @apply inline-flex items-center space-x-2 px-4 py-2 rounded-full;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.users-title {
  @apply text-4xl lg:text-5xl font-bold leading-tight;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
}

.users-subtitle {
  @apply text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto;
  font-family: "Inter", sans-serif;
}

.users-grid {
  @apply relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* User Cards */
.user-card {
  @apply bg-white rounded-3xl overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.user-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 25px 60px rgba(59, 130, 246, 0.15);
}

.user-card-header {
  @apply relative;
}

.user-card-image-container {
  @apply relative overflow-hidden;
  height: 200px;
}

.user-card-main-image {
  @apply w-full h-full object-contain transition-all duration-500;
  padding: 20px;
}

.user-card:hover .user-card-main-image {
  opacity: 0.3;
  transform: scale(1.05);
}

.user-card-overlay {
  @apply absolute inset-0 flex items-center justify-center opacity-0 transition-all duration-500;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.05)
  );
}

.user-card:hover .user-card-overlay {
  opacity: 1;
}

.overlay-logos {
  @apply flex flex-col space-y-4 px-6;
}

.overlay-logo {
  @apply w-full h-8 object-contain transition-all duration-300;
  filter: brightness(0.8);
}

.overlay-logo:hover {
  transform: scale(1.1);
  filter: brightness(1);
}

.user-card-icon {
  @apply absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center;
  border: 3px solid rgba(59, 130, 246, 0.1);
}

.user-card:hover .user-card-icon {
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(-50%) scale(1.1);
}

.icon-default {
  @apply w-8 h-8 transition-opacity duration-300;
}

.icon-hover {
  @apply w-8 h-8 absolute inset-0 m-auto opacity-0 transition-opacity duration-300;
}

.user-card:hover .icon-default {
  opacity: 0;
}

.user-card:hover .icon-hover {
  opacity: 1;
}

.user-card-content {
  @apply p-8 pt-12 text-center space-y-6;
}

.user-card-title {
  @apply text-2xl font-bold text-gray-800 mb-4;
  font-family: "Inter", sans-serif;
}

.user-card-description {
  @apply text-gray-600 leading-relaxed mb-6;
  font-family: "Inter", sans-serif;
}

.user-card-stats {
  @apply flex justify-center space-x-8 pt-4 border-t border-gray-100;
}

.user-card-stats .stat-item {
  @apply text-center;
}

.user-card-stats .stat-number {
  @apply block text-2xl font-bold text-GTI-BLUE-default mb-1;
  font-family: "Inter", sans-serif;
}

.user-card-stats .stat-label {
  @apply text-gray-500 text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* Modern Sectors Section */
.modern-sectors-section {
  @apply py-20 px-4 relative overflow-hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.modern-sectors-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 70% 20%,
      rgba(59, 130, 246, 0.04) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 30% 80%,
      rgba(6, 182, 212, 0.04) 0%,
      transparent 50%
    );
  z-index: 1;
}

.sectors-header {
  @apply relative z-10 text-center space-y-6 mb-16;
}

.sectors-badge {
  @apply inline-flex items-center space-x-2 px-4 py-2 rounded-full;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.sectors-title {
  @apply text-4xl lg:text-5xl font-bold leading-tight;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
}

.sectors-subtitle {
  @apply text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto;
  font-family: "Inter", sans-serif;
}

.sectors-grid {
  @apply relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Sector Cards */
.sector-card {
  @apply bg-white rounded-3xl overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-3;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.sector-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 30px 70px rgba(59, 130, 246, 0.2);
}

.sector-card-header {
  @apply relative overflow-hidden;
  height: 180px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.05)
  );
}

.sector-icon-container {
  @apply absolute inset-0 flex items-center justify-center;
}

.sector-icon {
  @apply w-20 h-20 object-contain transition-all duration-500;
  filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.2));
}

.sector-card:hover .sector-icon {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 8px 20px rgba(59, 130, 246, 0.3));
}

.sector-overlay {
  @apply absolute inset-0 flex items-center justify-center opacity-0 transition-all duration-500;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.9),
    rgba(6, 182, 212, 0.9)
  );
}

.sector-card:hover .sector-overlay {
  opacity: 1;
}

.overlay-content {
  @apply text-center;
}

.overlay-text {
  @apply text-white text-xl font-bold;
  font-family: "Inter", sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sector-card-content {
  @apply p-8 space-y-6;
}

.sector-card-title {
  @apply text-2xl font-bold text-gray-800 text-center;
  font-family: "Inter", sans-serif;
}

.sector-card-description {
  @apply text-gray-600 leading-relaxed text-center;
  font-family: "Inter", sans-serif;
}

.sector-features {
  @apply flex flex-wrap justify-center gap-2 pt-4;
}

.feature-tag {
  @apply px-3 py-1 text-xs font-medium rounded-full transition-all duration-300;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
  font-family: "Inter", sans-serif;
}

.feature-tag:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(6, 182, 212, 0.2)
  );
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

/* Innovative Latest Section */
.innovative-latest-section {
  @apply py-24 px-4 relative overflow-hidden;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
}

.innovative-latest-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(6, 182, 212, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(168, 85, 247, 0.05) 0%,
      transparent 50%
    );
  z-index: 1;
}

.innovative-latest-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  z-index: 1;
}

/* Header */
.latest-header-innovative {
  @apply relative z-10 mb-16 text-center;
}

.header-content {
  @apply max-w-4xl mx-auto;
}

.latest-title-innovative {
  @apply text-4xl lg:text-6xl font-bold leading-tight mb-6;
  background: linear-gradient(135deg, #ffffff, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Inter", sans-serif;
}

.latest-subtitle-innovative {
  @apply text-xl text-gray-300 leading-relaxed;
  font-family: "Inter", sans-serif;
}

/* Dashboard Grid */
.latest-dashboard-grid {
  @apply relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

/* Dashboard Cards */
.dashboard-card {
  @apply rounded-3xl overflow-hidden transition-all duration-500 hover:-translate-y-2 cursor-pointer;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  min-height: 420px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.dashboard-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  border-radius: inherit;
  z-index: -1;
  transition: all 0.3s ease;
}

.dashboard-card:hover::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
}

.dashboard-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

/* Card Header */
.card-header-dashboard {
  @apply p-6 flex items-center justify-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
  @apply flex items-center space-x-4;
}

.category-icon {
  @apply w-12 h-12 rounded-xl flex items-center justify-center;
  transition: all 0.3s ease;
}

.category-icon svg {
  @apply w-6 h-6;
}

.tech-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.opp-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.innovation-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.news-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.dashboard-card:hover .category-icon {
  transform: scale(1.1) rotate(5deg);
}

.header-text h3 {
  @apply text-lg font-bold text-gray-800 mb-1;
  font-family: "Inter", sans-serif;
}

.dashboard-card-title {
  @apply text-lg font-bold text-gray-800 mb-1;
  font-family: "Inter", sans-serif;
}

.dashboard-card-subtitle {
  @apply text-gray-600 text-sm;
  font-family: "Inter", sans-serif;
}

.header-right {
  @apply flex items-center;
}

/* Indicators */
.category-badge {
  @apply px-3 py-1 rounded-full text-xs font-bold;
}

.tech-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.trend-indicator {
  @apply w-8 h-8 rounded-full flex items-center justify-center;
}

.trend-indicator.up {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.trend-indicator svg {
  @apply w-4 h-4;
}

.priority-badge {
  @apply px-3 py-1 rounded-full text-xs font-bold;
}

.priority-badge.high {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.time-indicator {
  @apply flex items-center space-x-2 text-gray-500 text-xs;
}

.time-indicator svg {
  @apply w-4 h-4;
}

/* Card Content */
.card-content-dashboard {
  @apply flex-1 p-6 pt-4;
}

.content-items {
  @apply space-y-3;
}

/* Card Footer */
.card-footer-dashboard {
  @apply p-6 pt-0 flex items-center justify-between border-t border-gray-100 mt-auto;
}

.footer-stats {
  @apply text-gray-500 text-sm;
  font-family: "Inter", sans-serif;
}

.footer-action {
  @apply flex items-center space-x-2 text-GTI-BLUE-default font-semibold text-sm transition-all duration-300 hover:text-blue-700;
  font-family: "Inter", sans-serif;
}

.footer-action:hover {
  transform: translateX(5px);
}

.action-arrow {
  @apply w-4 h-4 transition-transform duration-300;
}

.footer-action:hover .action-arrow {
  transform: translateX(3px);
}

/* Card Type Specific Hover Effects */
.featured-tech-card:hover {
  box-shadow: 0 25px 60px rgba(16, 185, 129, 0.2);
}

.opportunities-card:hover {
  box-shadow: 0 25px 60px rgba(245, 158, 11, 0.2);
}

.innovation-card:hover {
  box-shadow: 0 25px 60px rgba(139, 92, 246, 0.2);
}

.news-card:hover {
  box-shadow: 0 25px 60px rgba(239, 68, 68, 0.2);
}

/* Button Animations */
.button-text {
  @apply relative z-10;
}

.button-icon {
  @apply text-xl relative z-10;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Modern Stats Section */
.homepage-stats-container {
  @apply relative py-20 px-4 overflow-hidden;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.homepage-stats-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(6, 182, 212, 0.1) 0%,
      transparent 50%
    );
  z-index: 1;
}

.homepage-stats-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto relative z-10;
}

.homepage-stat-item {
  @apply relative p-8 rounded-3xl text-center transition-all duration-500 hover:scale-105 cursor-pointer;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.homepage-stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border-radius: 24px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.homepage-stat-item:hover::before {
  opacity: 1;
}

.homepage-stat-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.2);
}

.homepage-stat-number {
  @apply text-4xl md:text-5xl font-bold mb-3 text-white;
  font-family: "Inter", sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: numberGlow 2s ease-in-out infinite alternate;
}

@keyframes numberGlow {
  from {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
  }
  to {
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.6));
  }
}

.homepage-stat-label {
  @apply text-gray-300 text-base md:text-lg font-medium;
  font-family: "Inter", sans-serif;
  transition: color 0.3s ease;
}

.homepage-stat-item:hover .homepage-stat-label {
  @apply text-white;
}

/* Animated Counter */
.animated-counter {
  display: inline-block;
  transition: all 0.3s ease;
}

.counter-icon {
  @apply w-12 h-12 mx-auto mb-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300;
}

/* Interactive Elements and Animations */
.homepage-interactive-element {
  @apply transition-all duration-300 ease-in-out;
}

.homepage-interactive-element:hover {
  @apply transform -translate-y-1;
}

/* Floating Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.homepage-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.homepage-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Gradient Animation */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.homepage-gradient-animate {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Stagger Animation for Cards */
.homepage-stagger-item {
  opacity: 0;
  transform: translateY(30px);
  animation: stagger-in 0.6s ease-out forwards;
}

.homepage-stagger-item:nth-child(1) {
  animation-delay: 0.1s;
}
.homepage-stagger-item:nth-child(2) {
  animation-delay: 0.2s;
}
.homepage-stagger-item:nth-child(3) {
  animation-delay: 0.3s;
}
.homepage-stagger-item:nth-child(4) {
  animation-delay: 0.4s;
}
.homepage-stagger-item:nth-child(5) {
  animation-delay: 0.5s;
}
.homepage-stagger-item:nth-child(6) {
  animation-delay: 0.6s;
}

@keyframes stagger-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Typography */
.homepage-heading-primary {
  @apply text-4xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight;
  font-family: "Montserrat", sans-serif;
}

.homepage-heading-secondary {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.homepage-heading-tertiary {
  @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-tight;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.homepage-body-large {
  @apply text-lg md:text-xl leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.homepage-body-medium {
  @apply text-base md:text-lg leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.homepage-body-small {
  @apply text-sm md:text-base leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

/* Color Scheme Enhancements */
.homepage-text-primary {
  @apply text-GTI-BLUE-default;
}

.homepage-text-secondary {
  @apply text-gray-700;
}

.homepage-text-muted {
  @apply text-gray-500;
}

.homepage-text-light {
  @apply text-white;
}

.homepage-bg-primary {
  @apply bg-GTI-BLUE-default;
}

.homepage-bg-gradient-primary {
  @apply bg-gradient-to-r from-GTI-BLUE-default to-blue-600;
}

.homepage-bg-gradient-secondary {
  @apply bg-gradient-to-br from-blue-50 to-indigo-100;
}

/* Enhanced Button Hover Effects */
.homepage-cta-button:hover {
  @apply shadow-2xl;
  transform: translateY(-2px) scale(1.02);
}

.homepage-cta-button:active {
  transform: translateY(0) scale(0.98);
}

/* Enhanced Card Hover Effects */
.homepage-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 60px rgba(59, 130, 246, 0.15);
}

.homepage-card:hover .homepage-card-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(6, 182, 212, 0.2)
  );
}

/* Stagger Animation for Service Cards */
.homepage-card:nth-child(1) {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.homepage-card:nth-child(2) {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.homepage-card:nth-child(3) {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* Modern Latest Content Cards */
.modern-latest-card {
  @apply relative flex items-start space-x-4 p-4 rounded-2xl transition-all duration-300 cursor-pointer;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.modern-latest-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.modern-latest-card-image {
  @apply relative w-16 h-16 rounded-xl overflow-hidden flex-shrink-0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
}

.modern-latest-card-image img {
  transition: transform 0.3s ease;
}

.modern-latest-card:hover .modern-latest-card-image img {
  transform: scale(1.1);
}

.modern-latest-card-overlay {
  @apply absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center;
}

.modern-latest-card-badge {
  @apply text-2xl transform scale-0 group-hover:scale-100 transition-transform duration-300;
}

.modern-latest-card-content {
  @apply flex-1 min-w-0;
}

.modern-latest-card-date {
  @apply text-xs text-gray-500 mb-1 font-medium;
  font-family: "Inter", sans-serif;
}

.modern-latest-card-title {
  @apply font-bold text-sm mb-2 transition-colors duration-300 line-clamp-1;
  color: #1e293b;
  font-family: "Inter", sans-serif;
}

.modern-latest-card:hover .modern-latest-card-title {
  @apply text-GTI-BLUE-default;
}

.modern-latest-card-description {
  @apply text-xs text-gray-600 line-clamp-2 leading-relaxed;
  font-family: "Inter", sans-serif;
}

.modern-latest-card:hover .modern-latest-card-description {
  @apply text-gray-700;
}

/* Scroll-Triggered Animations */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-slide-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.scroll-slide-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scroll-scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Modern Gradient Overlays */
.gradient-overlay-blue {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.1)
  );
}

.gradient-overlay-teal {
  background: linear-gradient(
    135deg,
    rgba(6, 182, 212, 0.1),
    rgba(20, 184, 166, 0.1)
  );
}

.gradient-overlay-cyan {
  background: linear-gradient(
    135deg,
    rgba(6, 182, 212, 0.1),
    rgba(59, 130, 246, 0.1)
  );
}

/* Enhanced Section Spacing */
.homepage-section {
  @apply py-20 px-4 relative;
}

.homepage-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(59, 130, 246, 0.03) 0%,
    transparent 70%
  );
  pointer-events: none;
}

/* Modern Grid Layouts */
.modern-grid {
  @apply grid gap-8;
}

.modern-grid-2 {
  @apply grid-cols-1 md:grid-cols-2;
}

.modern-grid-3 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.modern-grid-4 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
}

/* Parallax Elements */
.parallax-slow {
  transform: translateZ(0);
  will-change: transform;
}

/* Modern Shadows */
.shadow-modern {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.shadow-modern-lg {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.shadow-modern-xl {
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
}

/* Interactive Micro-animations */
.bounce-subtle {
  animation: bounceSubtle 2s ease-in-out infinite;
}

@keyframes bounceSubtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.rotate-slow {
  animation: rotateSlow 20s linear infinite;
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

/* Magnetic Hover Effect */
.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-hover:hover {
  transform: scale(1.05) rotate(2deg);
}

/* Ripple Effect */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:hover::before {
  width: 300px;
  height: 300px;
}

/* Tilt Effect */
.tilt-effect {
  transition: transform 0.3s ease;
}

.tilt-effect:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

/* Breathing Animation */
.breathing {
  animation: breathing 4s ease-in-out infinite;
}

@keyframes breathing {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Globe Feature Hover Effects */
.homepage-globe-feature:hover {
  transform: scale(1.05);
  @apply shadow-2xl bg-gradient-to-br from-white to-blue-50;
}

/* Text Reveal Animation */
.homepage-text-reveal {
  overflow: hidden;
}

.homepage-text-reveal::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  animation: text-reveal 2s ease-in-out;
}

@keyframes text-reveal {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Extra Small Mobile Responsiveness */
@media (max-width: 480px) {
  .modern-hero-content {
    @apply px-2;
  }

  .modern-hero-left {
    @apply text-center;
  }

  .modern-hero-left > div:first-child {
    @apply flex flex-col justify-center items-center;
  }

  .feature-card {
    max-width: 140px;
    @apply p-2;
  }

  .feature-title {
    @apply text-xs;
  }

  .feature-description {
    @apply text-xs;
  }
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
  .homepage-hero-section {
    min-height: 60vh;
    @apply py-8 px-4;
  }

  .homepage-section {
    @apply py-8 px-4;
  }

  .homepage-hero-content {
    @apply px-4;
  }

  /* Hero Section Mobile */
  .homepage-hero-section {
    @apply py-8;
    min-height: 100vh;
  }

  .homepage-hero-title {
    @apply text-3xl leading-tight mb-3;
  }

  .homepage-hero-subtitle {
    @apply text-base mb-4;
  }

  .homepage-cta-button {
    @apply px-6 py-3 text-sm;
  }

  .homepage-heading-primary {
    @apply text-3xl leading-tight;
  }

  .homepage-heading-secondary {
    @apply text-2xl;
  }

  .homepage-heading-tertiary {
    @apply text-xl;
  }

  .homepage-body-large {
    @apply text-base;
  }

  .homepage-body-medium {
    @apply text-sm;
  }

  .homepage-card {
    @apply mx-2;
  }

  .homepage-card:hover {
    transform: translateY(-2px) scale(1.005);
  }

  .homepage-cta-button {
    @apply px-6 py-3 text-base;
  }

  .homepage-globe-container {
    @apply px-4;
  }

  .homepage-globe-feature {
    @apply p-3 w-40;
  }

  .homepage-globe-feature-title {
    @apply text-xs;
  }

  .homepage-globe-feature-description {
    @apply text-xs leading-tight;
  }

  /* Mobile Grid Adjustments */
  .homepage-stats-grid {
    @apply grid-cols-2 gap-4;
  }

  /* Mobile Card Spacing */
  .homepage-card-content {
    @apply p-4;
  }

  .homepage-card-header {
    @apply p-4;
  }

  /* Enhanced Mobile Features */
  .modern-latest-card {
    @apply p-3 space-x-3;
  }

  .modern-latest-card-image {
    @apply w-14 h-14;
  }

  .modern-get-started-button {
    @apply px-8 py-4 text-base;
  }

  /* Particle Effects - Reduced for Mobile */
  .hero-particles {
    opacity: 0.5;
  }

  .particle {
    animation-duration: 20s;
  }

  /* Floating Elements - Smaller on Mobile */
  .floating-element {
    transform: scale(0.7);
  }

  /* Mobile Animation Adjustments */
  .homepage-float {
    animation: float 4s ease-in-out infinite;
  }

  .scroll-fade-in,
  .scroll-slide-left,
  .scroll-slide-right,
  .scroll-scale-in {
    transition-duration: 0.6s;
  }

  .homepage-stagger-item {
    animation-delay: 0.1s;
  }

  .homepage-stagger-item:nth-child(n + 4) {
    animation-delay: 0.2s;
  }

  /* Showcase Mobile */
  .showcase-benefits {
    @apply grid-cols-1 gap-6;
  }

  .showcase-stats {
    @apply grid-cols-2 gap-6;
  }

  .showcase-title {
    @apply text-3xl;
  }

  .showcase-subtitle {
    @apply text-lg;
  }

  .showcase-button {
    @apply px-8 py-4 text-lg;
  }

  .benefit-card {
    @apply p-6;
  }

  .benefit-icon {
    @apply text-4xl w-16 h-16;
  }

  /* Supply Chain Mobile */
  .supply-chain-content {
    @apply grid-cols-1 gap-12;
  }

  .supply-chain-title {
    @apply text-3xl;
  }

  .supply-chain-subtitle {
    @apply text-lg;
  }

  .supply-chain-button {
    @apply px-8 py-3 text-base;
  }

  .info-card {
    @apply p-6;
  }

  .info-description {
    @apply text-base;
  }

  .supply-chain-image {
    @apply max-w-sm;
  }

  /* Innovative Latest Section Mobile */
  .latest-title-innovative {
    @apply text-3xl lg:text-4xl;
  }

  .latest-subtitle-innovative {
    @apply text-lg;
  }

  .latest-dashboard-grid {
    @apply grid-cols-1 gap-4;
  }

  .dashboard-card {
    min-height: 380px;
  }

  .card-header-dashboard {
    @apply flex-col items-start space-y-3 p-4;
  }

  .header-left {
    @apply space-x-3;
  }

  .category-icon {
    @apply w-10 h-10;
  }

  .dashboard-card-title {
    @apply text-base;
  }

  .dashboard-card-subtitle {
    @apply text-xs;
  }
}

/* Tablet Responsiveness */
@media (min-width: 641px) and (max-width: 1024px) {
  .homepage-hero-section {
    @apply py-16;
  }

  .homepage-section {
    @apply py-12;
  }

  .homepage-heading-primary {
    @apply text-5xl;
  }

  .homepage-heading-secondary {
    @apply text-3xl;
  }

  .homepage-globe-feature {
    @apply w-44;
  }

  .homepage-stats-grid {
    @apply grid-cols-2 gap-6;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1280px) {
  .homepage-hero-section {
    @apply py-20;
  }

  .homepage-section {
    @apply py-20;
  }

  .homepage-stats-grid {
    @apply gap-12;
  }

  .homepage-card {
    @apply p-8;
  }
}
