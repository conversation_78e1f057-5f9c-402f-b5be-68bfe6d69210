/* B2Match Navbar Container */
.b2match-navbar-container {
  @apply fixed top-0 left-0 right-0 z-50 flex items-center justify-between w-full px-4 lg:px-8;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  height: 64px;
  transition: all 0.3s ease;
}

/* Mobile-specific navbar adjustments */
@media (max-width: 640px) {
  .b2match-navbar-container {
    height: 56px;
    @apply px-3;
  }
}

/* Remove the gradient overlay for cleaner B2Match look */

.b2match-navbar-content {
  @apply flex items-center justify-between flex-1 ml-4;
  transition: all 0.3s ease;
}

.b2match-navbar-content.mobile-closed {
  @apply hidden lg:flex;
}

.b2match-navbar-content.mobile-open {
  @apply flex flex-col lg:flex-row absolute lg:relative top-[64px] lg:top-0 left-0 right-0 bg-white lg:bg-transparent p-4 sm:p-6 lg:p-0 shadow-lg lg:shadow-none z-50;
  border-top: 1px solid #e5e7eb;
  animation: slideDown 0.3s ease;
  max-height: calc(100vh - 64px);
  overflow-y: auto;
}

/* Mobile-specific mobile menu adjustments */
@media (max-width: 640px) {
  .b2match-navbar-content.mobile-open {
    top: 56px;
    max-height: calc(100vh - 56px);
    @apply p-4;
  }
}

.b2match-navbar-overlay {
  @apply fixed inset-0 bg-black/20 z-40 lg:hidden;
  top: 64px;
  transition: all 0.3s ease;
}

/* Mobile-specific overlay adjustments */
@media (max-width: 640px) {
  .b2match-navbar-overlay {
    top: 56px;
  }
}

/* B2Match Navigation Menu */
.b2match-nav-menu {
  @apply flex justify-center items-center flex-1;
}

.b2match-nav-list {
  @apply flex items-center space-x-1;
}

.b2match-nav-item {
  @apply relative;
}

/* B2Match Navigation Links */
.b2match-nav-link {
  @apply relative px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-300 ease-in-out;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  text-decoration: none;
  white-space: nowrap;
  border-radius: 6px;
  transform: translateY(0);
}

.b2match-nav-link:hover:not(.active) {
  @apply text-blue-600 bg-blue-50;
  transform: translateY(-1px);
}

.b2match-nav-link.active {
  @apply text-white bg-blue-600 font-semibold shadow-md;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.b2match-nav-link.active:hover {
  @apply text-white;
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15), 0 3px 6px -1px rgba(0, 0, 0, 0.1);
}

.item::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(147, 51, 234, 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.item:hover::before {
  width: 90%;
}

.item:hover::after {
  opacity: 1;
}

.item:hover {
  @apply text-GTI-BLUE-default transform -translate-y-1 shadow-lg;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.item:active {
  transform: translateY(0) scale(0.98);
}

/* Active Navigation Item */
.item.active {
  @apply text-GTI-BLUE-default bg-white shadow-md;
}

.item.active::before {
  width: 90%;
}

.item.active::after {
  opacity: 0.7;
}

/* Legacy styles - keeping for backward compatibility */

/* B2Match Dropdown Styles */
.b2match-dropdown-container {
  @apply relative;
}

.b2match-dropdown-button {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-200 ease-in-out;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 6px;
}

.b2match-dropdown-button:hover {
  @apply text-blue-600 bg-blue-50;
}

/* Active state for dropdown buttons when dropdown is open */
.b2match-dropdown-container:hover .b2match-dropdown-button {
  @apply text-white bg-blue-600;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.b2match-dropdown-text {
  @apply mr-1;
}

.b2match-dropdown-arrow {
  @apply w-4 h-4 transition-transform duration-200;
  margin-left: 4px;
}

.b2match-dropdown {
  @apply fixed left-0 right-0 z-50;
  top: 64px; /* Height of navbar */
  transition: all 0.2s ease-in-out;
  width: 100vw;
}

.b2match-dropdown.show {
  @apply opacity-100 visible;
}

.b2match-dropdown.hide {
  @apply opacity-0 invisible;
}

.b2match-dropdown-menu {
  @apply bg-white shadow-xl border-t border-gray-200;
  padding: 48px 0;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

.b2match-dropdown-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-12;
}

.b2match-dropdown-section {
  @apply space-y-6;
}

.b2match-dropdown-header {
  @apply text-sm font-semibold text-gray-400 uppercase tracking-wider mb-4;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: 0.1em;
}

.b2match-dropdown-item {
  @apply flex items-start p-4 rounded-lg hover:bg-blue-50 transition-all duration-200;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  border: 1px solid transparent;
}

.b2match-dropdown-item:hover {
  border-color: #e0e7ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.b2match-dropdown-icon {
  @apply text-2xl mr-4 mt-1 flex-shrink-0;
  filter: grayscale(0.3);
  transition: all 0.2s ease;
}

.b2match-dropdown-content {
  @apply flex-1;
}

.b2match-dropdown-title {
  @apply text-base font-semibold text-gray-900 mb-2;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.4;
}

.b2match-dropdown-description {
  @apply text-sm text-gray-600 leading-relaxed;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.5;
}

/* B2Match Dropdown Hover Effects */
.b2match-dropdown-item:hover .b2match-dropdown-title {
  @apply text-blue-700;
}

.b2match-dropdown-item:hover .b2match-dropdown-icon {
  filter: grayscale(0);
  transform: scale(1.05);
}

.b2match-dropdown-item:hover .b2match-dropdown-description {
  @apply text-gray-700;
}

/* B2Match Full-Width Dropdown Background */
.b2match-dropdown::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.02);
  z-index: -1;
}

/* B2Match Dropdown Overlay */
.b2match-dropdown-overlay {
  @apply fixed inset-0 bg-black bg-opacity-5 z-40;
  top: 64px; /* Height of navbar */
}

/* B2Match Hover Behavior */
.b2match-dropdown-container {
  @apply relative;
}

.b2match-dropdown-container:hover .b2match-dropdown {
  @apply opacity-100 visible;
}

.b2match-dropdown-container:hover .b2match-dropdown-arrow {
  transform: rotate(180deg);
}

/* Smooth hover transitions */
.b2match-dropdown-container .b2match-dropdown {
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.b2match-dropdown-container .b2match-dropdown-arrow {
  transition: transform 0.2s ease-in-out;
}

/* Ensure dropdown stays open when hovering over dropdown content */
.b2match-dropdown:hover {
  @apply opacity-100 visible;
}

/* Prevent gap between button and dropdown */
.b2match-dropdown-container .b2match-dropdown {
  margin-top: 0;
}

/* B2Match Logo Styles */
.b2match-logo-container {
  @apply flex items-center;
}

.logo-wrapper {
  @apply cursor-pointer;
}

.b2match-logo {
  @apply h-8 w-auto;
  max-height: 32px;
}

/* B2Match Mobile Toggle */
.b2match-mobile-toggle {
  @apply lg:hidden flex flex-col justify-center items-center w-8 h-8 ml-4 cursor-pointer;
  background: none;
  border: none;
}

.hamburger-line {
  @apply w-6 h-0.5 bg-gray-700 transition-all duration-300 ease-in-out;
  margin: 2px 0;
}

/* B2Match User Actions */
.b2match-user-actions {
  @apply flex items-center space-x-4;
}

.b2match-user-menu {
  @apply flex items-center space-x-3;
}

.b2match-auth-actions {
  @apply flex items-center space-x-3;
}

/* B2Match Notification Button */
.b2match-notification-button {
  @apply relative p-2 text-gray-600 hover:text-gray-900 transition-colors duration-200;
  background: none;
  border: none;
  cursor: pointer;
}

.b2match-notification-icon {
  @apply w-6 h-6;
}

.b2match-notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center;
  font-size: 10px;
  line-height: 1;
}

/* B2Match Profile Button */
.b2match-profile-button {
  @apply flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-200;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
}

.b2match-profile-button:hover {
  @apply bg-gray-50;
}

.b2match-profile-avatar {
  @apply w-8 h-8 rounded-full object-cover;
}

.b2match-profile-name {
  @apply text-sm font-medium;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* B2Match Get Started Button */
.b2match-get-started-button {
  @apply px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  border: none;
  cursor: pointer;
}

/* B2Match Responsive Design */
@media (max-width: 1024px) {
  .b2match-nav-list {
    @apply space-x-1;
  }

  .b2match-nav-link {
    @apply px-3 py-2 text-sm;
  }

  .b2match-dropdown-button {
    @apply px-3 py-2 text-sm;
  }

  .b2match-dropdown-menu {
    padding: 32px 1.5rem;
  }

  .b2match-dropdown-grid {
    @apply grid-cols-1 md:grid-cols-2 gap-8;
  }
}

@media (max-width: 900px) {
  .b2match-dropdown-menu {
    padding: 24px 1rem;
  }

  .b2match-dropdown-grid {
    @apply grid-cols-1 gap-6;
  }
}

@media (max-width: 768px) {
  .b2match-navbar-container {
    @apply px-3;
  }

  .b2match-nav-menu {
    @apply flex-col items-start space-y-2 w-full;
  }

  .b2match-nav-list {
    @apply flex-col space-x-0 space-y-2 w-full;
  }

  .b2match-nav-item {
    @apply w-full;
  }

  .b2match-nav-link {
    @apply w-full px-4 py-3 text-base;
  }

  .b2match-nav-link.active {
    @apply text-white bg-blue-600 font-semibold shadow-md;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
  }

  .b2match-dropdown-container {
    @apply w-full;
  }

  .b2match-dropdown-button {
    @apply w-full px-4 py-3 text-base justify-between;
  }

  .b2match-dropdown {
    @apply relative top-0 left-0 w-full mt-2;
    position: relative;
    width: 100%;
  }

  .b2match-dropdown-menu {
    @apply shadow-md;
    padding: 20px 1rem;
    max-width: none;
  }

  .b2match-dropdown-grid {
    @apply grid-cols-1 gap-4;
  }

  .b2match-dropdown-item {
    @apply px-4 py-3;
  }

  .b2match-dropdown-title {
    @apply text-base;
  }

  .b2match-dropdown-description {
    @apply text-sm;
  }

  .b2match-user-actions {
    @apply flex-col space-x-0 space-y-3 w-full;
  }

  .b2match-user-menu {
    @apply flex-col space-x-0 space-y-3 w-full;
  }

  .b2match-profile-button {
    @apply w-full justify-start px-4 py-3;
  }

  .b2match-get-started-button {
    @apply w-full py-3 text-base;
  }
}

/* B2Match Navbar Scrolled State */
.b2match-navbar-container.navbar-scrolled {
  @apply shadow-md;
  border-bottom: 1px solid #d1d5db;
}

/* B2Match Focus States for Accessibility */
.b2match-nav-link:focus,
.b2match-dropdown-button:focus,
.b2match-dropdown-item:focus,
.b2match-notification-button:focus,
.b2match-profile-button:focus,
.b2match-get-started-button:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* B2Match Animation Keyframes */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* B2Match Dropdown Animation */
.b2match-dropdown.show {
  animation: slideDown 0.2s ease-out;
}

.b2match-dropdown.show .b2match-dropdown-item {
  animation: fadeInUp 0.3s ease-out both;
}

.b2match-dropdown.show .b2match-dropdown-item:nth-child(1) {
  animation-delay: 0.05s;
}

.b2match-dropdown.show .b2match-dropdown-item:nth-child(2) {
  animation-delay: 0.1s;
}

.b2match-dropdown.show .b2match-dropdown-item:nth-child(3) {
  animation-delay: 0.15s;
}

.b2match-dropdown.show .b2match-dropdown-item:nth-child(4) {
  animation-delay: 0.2s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* B2Match Mobile Menu Animation */
.b2match-navbar-content.mobile-open {
  animation: slideDown 0.3s ease-out;
}

/* Dropdown Button Styles */
.dropdown-button {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-xl transition-all duration-300 ease-out;
  font-family: "Inter", "Plus Jakarta Sans", sans-serif;
  letter-spacing: -0.01em;
  white-space: nowrap;
}

.dropdown-button:hover {
  @apply text-GTI-BLUE-default transform -translate-y-1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

/* Modern Dropdown Styles */
.modern-dropdown {
  @apply absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-64 z-50;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-dropdown.show {
  @apply opacity-100 visible scale-100;
}

.modern-dropdown.hide {
  @apply opacity-0 invisible scale-95 pointer-events-none;
}

.modern-dropdown-menu {
  @apply bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  );
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modern-dropdown-item-wrapper {
  @apply border-b border-gray-100/50 last:border-b-0;
}

.modern-dropdown-item {
  @apply flex items-center w-full px-6 py-4 text-left transition-all duration-300;
  font-family: "Inter", sans-serif;
  text-decoration: none;
}

.modern-dropdown-item-wrapper:hover .modern-dropdown-item {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 transform translate-x-1;
}

.dropdown-icon {
  @apply text-xl mr-4 transition-transform duration-300;
  filter: grayscale(0.3);
}

.modern-dropdown-item:hover .dropdown-icon {
  filter: grayscale(0);
  transform: scale(1.1);
}

.dropdown-text {
  @apply text-gray-700 font-medium text-sm transition-colors duration-300;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.01em;
}

.modern-dropdown-item:hover .dropdown-text {
  @apply text-GTI-BLUE-default;
}

/* Dropdown Animation Effects */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modern-dropdown.show .modern-dropdown-menu {
  animation: dropdownSlideIn 0.3s ease-out;
}

/* Stagger animation for dropdown items */
.modern-dropdown-item-wrapper:nth-child(1) .modern-dropdown-item {
  animation-delay: 0.05s;
}

.modern-dropdown-item-wrapper:nth-child(2) .modern-dropdown-item {
  animation-delay: 0.1s;
}

.modern-dropdown-item-wrapper:nth-child(3) .modern-dropdown-item {
  animation-delay: 0.15s;
}

.modern-dropdown-item-wrapper:nth-child(4) .modern-dropdown-item {
  animation-delay: 0.2s;
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-dropdown.show .modern-dropdown-item {
  animation: itemSlideIn 0.3s ease-out both;
}

.item2 {
  @apply flex flex-row items-center lg:mx-3 px-3 py-2 rounded-xl transition-all duration-300;
}

.item2:hover {
  @apply bg-white/10 backdrop-blur-sm transform scale-105;
}

/* Enhanced Modal Buttons */
.signModalActive {
  @apply text-GTI-BLUE-default bg-white border-2 border-GTI-BLUE-default rounded-xl px-6 py-3 font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.signModal {
  @apply bg-GTI-BLUE-default text-white rounded-xl px-6 py-3 font-semibold transition-all duration-300 transform hover:scale-105 hover:bg-blue-700 hover:shadow-xl;
  position: relative;
  overflow: hidden;
}

.signModal::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.signModal:hover::before {
  left: 100%;
}

/* Dropdown Animations */
.dropdown-menu {
  @apply opacity-0 invisible transform scale-95 transition-all duration-200 ease-out;
}

.dropdown-menu.show {
  @apply opacity-100 visible transform scale-100;
}

.dropdown-item {
  @apply transition-all duration-200 ease-out transform hover:translate-x-2 hover:bg-blue-50;
}

/* Enhanced Mobile Navigation */

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-nav-list {
  @apply flex items-center space-x-2 lg:space-x-4;
  flex-wrap: wrap;
}

/* Mobile Navigation List */
@media (max-width: 1023px) {
  .modern-nav-list {
    @apply flex-col space-x-0 space-y-3 w-full;
  }

  .modern-nav-list li {
    @apply w-full;
  }

  .item {
    @apply w-full text-center py-4 px-6 text-base font-medium rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropdown-button {
    @apply w-full text-center py-4 px-6 text-base font-medium rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-dropdown {
    @apply relative top-0 left-0 transform-none mt-2 w-full;
  }

  .modern-dropdown-menu {
    @apply shadow-lg border border-gray-200 rounded-xl;
  }

  .modern-dropdown-item {
    @apply px-6 py-4 text-base rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .dropdown-icon {
    @apply text-xl mr-4;
  }
}

/* Extra mobile adjustments for very small screens */
@media (max-width: 640px) {
  .modern-nav-list {
    @apply space-y-2;
  }

  .item {
    @apply py-3 px-4 text-sm;
    min-height: 44px;
  }

  .dropdown-button {
    @apply py-3 px-4 text-sm;
    min-height: 44px;
  }

  .modern-dropdown-item {
    @apply px-4 py-3 text-sm;
    min-height: 44px;
  }
}

/* Mobile Menu Animations */
.mobile-menu {
  @apply transform transition-all duration-300 ease-in-out;
}

.mobile-menu.open {
  @apply translate-x-0;
}

.mobile-menu.closed {
  @apply -translate-x-full;
}

/* Modern Logo Container */
.modern-logo-container {
  @apply flex items-center justify-between relative z-10;
}

.logo-wrapper {
  @apply cursor-pointer transition-all duration-300 ease-out;
}

.logo-wrapper:hover {
  @apply transform scale-105;
}

.modern-logo {
  @apply h-10 w-auto object-contain transition-all duration-300;
  max-width: 140px;
  min-height: 32px;
}

.modern-logo:hover {
  transform: scale(1.05);
}

/* Mobile logo adjustments */
@media (max-width: 640px) {
  .modern-logo {
    @apply h-8;
    max-width: 120px;
    min-height: 28px;
  }
}

/* Modern Mobile Toggle */
.modern-mobile-toggle {
  @apply lg:hidden flex flex-col justify-center items-center w-10 h-10 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:scale-105;
  -webkit-tap-highlight-color: transparent;
}

.hamburger-line {
  @apply w-5 h-0.5 bg-GTI-BLUE-default transition-all duration-300 ease-in-out;
  margin: 2px 0;
}

.modern-mobile-toggle:hover .hamburger-line {
  @apply bg-blue-600;
}

/* Mobile hamburger adjustments */
@media (max-width: 640px) {
  .modern-mobile-toggle {
    @apply w-9 h-9;
  }

  .hamburger-line {
    @apply w-4;
  }
}

/* Modern User Actions */
.modern-user-actions {
  @apply flex items-center justify-end;
}

.modern-user-menu {
  @apply flex items-center space-x-4;
}

.modern-notification-button {
  @apply relative p-2 rounded-xl bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 hover:scale-105;
}

.modern-notification-icon {
  @apply w-6 h-6 text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200;
}

.modern-notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center;
  font-size: 10px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.modern-profile-button {
  @apply flex items-center space-x-3 px-4 py-2 rounded-xl bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 hover:scale-105 max-w-[200px];
}

.modern-profile-avatar {
  @apply w-8 h-8 rounded-full object-cover border-2 border-white/50 hover:border-GTI-BLUE-default transition-colors duration-200;
}

.modern-profile-name {
  @apply text-gray-700 font-medium truncate text-sm;
  font-family: "Inter", sans-serif;
}

/* Modern Get Started Button */
.modern-get-started-button {
  @apply px-6 py-2.5 text-sm font-semibold text-white bg-GTI-BLUE-default rounded-xl transition-all duration-300 hover:bg-blue-600 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-offset-2;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.01em;
}

.modern-get-started-button:active {
  transform: scale(0.98);
}

/* Mobile User Actions */
@media (max-width: 1023px) {
  .modern-user-menu {
    @apply flex-col space-x-0 space-y-4 w-full mt-4;
  }

  .modern-profile-button {
    @apply w-full justify-center max-w-none py-4 rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-notification-button {
    @apply w-full justify-center py-4 rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-get-started-button {
    @apply w-full py-4 text-base font-semibold rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }
}

/* Extra mobile adjustments for user actions */
@media (max-width: 640px) {
  .modern-user-menu {
    @apply space-y-3 mt-3;
  }

  .modern-profile-button {
    @apply py-3 text-sm;
    min-height: 44px;
  }

  .modern-notification-button {
    @apply py-3;
    min-height: 44px;
  }

  .modern-get-started-button {
    @apply py-3 text-sm;
    min-height: 44px;
  }
}

/* Legacy Logo Animation (for backward compatibility) */
.logo-container {
  @apply transition-all duration-300 ease-out;
}

.logo-container:hover {
  @apply transform scale-110 rotate-3;
}

/* Modern Visual Effects and Animations */

/* Glassmorphism Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Floating Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Glow Effect */
.glow-effect {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth Scroll Navbar */
.navbar-scrolled {
  @apply shadow-2xl;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
}

/* Micro Interactions */
.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:active {
  transform: scale(0.95);
}

/* Gradient Text Effect */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Morphing Border */
.morphing-border {
  position: relative;
  overflow: hidden;
}

.morphing-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.morphing-border:hover::before {
  left: 100%;
}

/* Enhanced Focus States */
.focus-ring:focus {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2 ring-offset-white;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Prevent zoom on input focus on iOS */
  input,
  select,
  textarea {
    font-size: 16px !important;
  }

  /* Improve touch targets */
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile menu */
  .modern-navbar-content.mobile-open {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent horizontal scroll */
  .modern-navbar-container {
    overflow-x: hidden;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .item:hover {
    @apply transform-none;
  }

  .modern-mobile-toggle:hover {
    @apply transform-none;
  }

  .logo-wrapper:hover {
    @apply transform-none;
  }
}

/* Stagger Animation for Menu Items */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  animation: staggerIn 0.5s ease-out forwards;
}

.stagger-item:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-item:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-item:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-item:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes staggerIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Parallax Effect */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* Modern Scrollbar */
.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}
